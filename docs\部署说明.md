
# 部署更新 20250619 - 新增Web管理系统

## 快速更新（包含Web管理系统）
```bash
# 进入服务器目录
cd /var/www/cy-api

# 拉取最新代码
git pull

# 构建前端Web管理系统
cd web-admin
npm install
npm run build

# 返回根目录构建后端
cd ..
npx prisma generate

npm run build

# 重启服务
pm2 restart api-server


pm2 start ecosystem.config.js
```

## Web管理系统访问
- 管理界面：http://your-domain.com/web/
- 登录限制：只有MAIN角色的主账号可以登录

# 轻量后端API系统部署说明

## 1. 系统要求

- Ubuntu Server 20.04 LTS 或更高版本
- Node.js 18.x 或更高版本
- NPM 9.x 或更高版本
- SQLite 3.x

## 2. 安装基础环境

### 2.1 更新系统包
```bash
sudo apt update
sudo apt upgrade -y
```

### 2.2 安装 Node.js
```bash
# 添加 NodeSource 仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# 安装 Node.js
sudo apt install -y nodejs

# 验证安装
node --version
npm --version
```

### 2.3 安装 SQLite
```bash
sudo apt install -y sqlite3
```

## 3. 部署应用

### 3.1 创建应用目录
```bash
sudo mkdir -p /var/www/api-server
sudo chown -R $USER:$USER /var/www/api-server
```

### 3.2 上传项目文件
将项目文件上传到 `/var/www/api-server` 目录

### 3.3 安装依赖
```bash
cd /var/www/api-server
npm install
```

### 3.4 配置环境变量
```bash
# 创建 .env 文件
nano .env

# 添加以下配置
DATABASE_URL="file:/var/www/api-server/prisma/dev.db"
JWT_SECRET="your-secret-key"
PORT=3000
```

### 3.5 初始化数据库
```bash
# 生成 Prisma Client
npx prisma generate

# 执行数据库迁移
npx prisma migrate deploy

# 初始化种子数据
npm run db:seed
```

### 3.6 构建应用
```bash
npm run build
```

## 4. 配置进程管理器(PM2)

### 4.1 安装 PM2
```bash
sudo npm install -g pm2
```

### 4.2 创建 PM2 配置文件
```bash
# 创建 ecosystem.config.js
nano ecosystem.config.js
```

添加以下内容：
```javascript
module.exports = {
  apps: [{
    name: 'api-server',
    script: 'dist/src/main.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
```

### 4.3 启动应用
```bash
pm2 start ecosystem.config.js
```

### 4.4 配置开机自启
```bash
pm2 startup
pm2 save
```

## 5. 配置 Nginx 反向代理

### 5.1 安装 Nginx
```bash
sudo apt install -y nginx
```

### 5.2 配置 Nginx
```bash
# 创建站点配置
sudo nano /etc/nginx/sites-available/api-server
```

添加以下配置：
```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 5.3 启用站点配置
```bash
sudo ln -s /etc/nginx/sites-available/api-server /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 6. 验证部署

### 6.1 检查应用状态
```bash
pm2 status
pm2 logs
```

### 6.2 测试API接口
```bash
# 测试基础URL
curl http://your-domain.com

# 测试Swagger文档
curl http://your-domain.com/api-docs
```

## 7. 维护说明

### 7.1 日常维护命令
```bash
# 查看应用日志
pm2 logs api-server

# 重启应用
pm2 restart api-server

# 停止应用
pm2 stop api-server

# 删除应用
pm2 delete api-server
```

### 7.2 更新部署
```bash
cd /var/www/api-server
git pull  # 如果使用git管理
npm install
npm run build
pm2 restart api-server
```

### 7.3 数据库备份
```bash
# 备份数据库
cp /var/www/api-server/prisma/dev.db /backup/dev.db.$(date +%Y%m%d)
```

## 8. 安全建议

1. 配置防火墙，只开放必要端口
```bash
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw enable
```

2. 定期更新系统和依赖包
```bash
sudo apt update
sudo apt upgrade
npm audit
npm update
```

3. 使用 HTTPS
- 安装 Certbot
- 配置 SSL 证书
- 更新 Nginx 配置以支持 HTTPS

4. 设置文件权限
```bash
sudo chown -R www-data:www-data /var/www/api-server
sudo chmod -R 755 /var/www/api-server
```