import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user.module';
import { AccountModule } from './account/account.module';
import { StoreModule } from './store/store.module';
import { KeyvalueModule } from './keyvalue/keyvalue.module';
import { ImportModule } from './import/import.module';
import { QuotaModule } from './quota/quota.module';
import { WebModule } from './web/web.module';
import { TestAuthController } from './test-auth.controller';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', 'public', 'web'),
      serveRoot: '/web',
      serveStaticOptions: {
        // 为SPA路由提供fallback支持
        fallthrough: false,
        index: 'index.html',
      },
    }),
    PrismaModule,
    AuthModule,
    UserModule,
    AccountModule,
    StoreModule,
    KeyvalueModule,
    ImportModule,
    QuotaModule,
    WebModule, // 临时注释掉进行测试
  ],
  controllers: [AppController, TestAuthController],
  providers: [AppService],
})
export class AppModule {}
