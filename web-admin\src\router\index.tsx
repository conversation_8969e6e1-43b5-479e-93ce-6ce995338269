import { createBrowserRouter } from 'react-router-dom';
import Layout from '../components/Layout';
import Login from '../pages/Login';
import Dashboard from '../pages/Dashboard';
import SubUsers from '../pages/SubUsers';
import SubUserDetail from '../pages/SubUserDetail';
import QuotaSettings from '../pages/QuotaSettings';
import ImportData from '../pages/ImportData';
import ProtectedRoute from '../components/ProtectedRoute';

export const router = createBrowserRouter([
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <Layout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Dashboard />,
      },
      {
        path: 'dashboard',
        element: <Dashboard />,
      },
      {
        path: 'sub-users',
        element: <SubUsers />,
      },
      {
        path: 'sub-users/:id',
        element: <SubUserDetail />,
      },
      {
        path: 'quota-settings',
        element: <QuotaSettings />,
      },
      {
        path: 'import',
        element: <ImportData />,
      },
    ],
  },
], {
  basename: '/web'
});