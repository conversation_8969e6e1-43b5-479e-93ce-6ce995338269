import { Controller, Get, Res, Req } from '@nestjs/common';
import { Response, Request } from 'express';
import { join } from 'path';
import { Public } from './keyvalue/decorators/public.decorator';

@Controller()
export class AppController {

  @Public()
  @Get()
  redirectToWebDashboard(@Res() res: Response): void {
    res.redirect('/web/dashboard');
  }

  // 处理前端SPA路由的fallback
  @Public()
  @Get('web/:path(*)')
  serveFrontendApp(@Req() req: Request, @Res() res: Response): void {
    const requestPath = req.path;

    // 如果是静态资源，不处理（让静态文件服务处理）
    if (requestPath.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$/)) {
      res.status(404).send('Not Found');
      return;
    }

    // 对于前端路由，返回index.html
    const indexPath = join(__dirname, '..', '..', 'public', 'web', 'index.html');
    res.sendFile(indexPath);
  }
}
