import { <PERSON>, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import { AppService } from './app.service';
import { Public } from './keyvalue/decorators/public.decorator';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Public()
  @Get()
  redirectToWebDashboard(@Res() res: Response): void {
    res.redirect('/web/dashboard');
  }
}
