import { Controller, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import { join } from 'path';
import { Public } from './keyvalue/decorators/public.decorator';

@Controller()
export class AppController {

  @Public()
  @Get()
  redirectToWebDashboard(@Res() res: Response): void {
    res.redirect('/web/dashboard');
  }

  // 处理前端SPA路由的fallback - 只处理特定的前端路由
  @Public()
  @Get('web/dashboard')
  serveDashboard(@Res() res: Response): void {
    const indexPath = join(__dirname, '..', '..', 'public', 'web', 'index.html');
    res.sendFile(indexPath);
  }

  @Public()
  @Get('web/login')
  serveLogin(@Res() res: Response): void {
    const indexPath = join(__dirname, '..', '..', 'public', 'web', 'index.html');
    res.sendFile(indexPath);
  }

  @Public()
  @Get('web/sub-users')
  serveSubUsers(@Res() res: Response): void {
    const indexPath = join(__dirname, '..', '..', 'public', 'web', 'index.html');
    res.sendFile(indexPath);
  }

  @Public()
  @Get('web/sub-users/:id')
  serveSubUserDetail(@Res() res: Response): void {
    const indexPath = join(__dirname, '..', '..', 'public', 'web', 'index.html');
    res.sendFile(indexPath);
  }

  @Public()
  @Get('web/quota-settings')
  serveQuotaSettings(@Res() res: Response): void {
    const indexPath = join(__dirname, '..', '..', 'public', 'web', 'index.html');
    res.sendFile(indexPath);
  }

  @Public()
  @Get('web/import')
  serveImport(@Res() res: Response): void {
    const indexPath = join(__dirname, '..', '..', 'public', 'web', 'index.html');
    res.sendFile(indexPath);
  }
}
