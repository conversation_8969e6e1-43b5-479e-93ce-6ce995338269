import { Controller, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import { join } from 'path';
import { Public } from './keyvalue/decorators/public.decorator';

@Controller()
export class AppController {

  @Public()
  @Get()
  redirectToWebDashboard(@Res() res: Response): void {
    res.redirect('/web/dashboard');
  }

  // 处理前端路由的fallback，确保SPA路由正常工作
  @Public()
  @Get('web/*')
  serveFrontendApp(@Res() res: Response): void {
    const indexPath = join(__dirname, '..', '..', 'public', 'web', 'index.html');
    res.sendFile(indexPath);
  }
}
